<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Happi 演示站点 · 首页</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bangers&family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/styles.css" />
  </head>
  <body>
    <header>
      <div class="container nav">
        <a class="brand" href="index.html">
          <span class="brand-mark">:)</span>
          <span>HAPPI</span>
        </a>
        <nav>
          <ul>
            <li><a href="products.html">商店</a></li>
            <li><a href="story.html">关于我们</a></li>
            <li><a href="benefits.html">优势</a></li>
            <li><a href="subscribe.html">订阅</a></li>
          </ul>
        </nav>
      </div>
    </header>

    <main>
      <section class="hero">
        <div class="container hero-inner">
          <div class="reveal">
            <h1>Smooth & Zesty<br />橙味燕麦奶巧克力</h1><br /><br>
            <p>灵感来自长图的第一屏：充满活力的插画风与醒目的橙黄配色。此处为纯展示，无业务逻辑。</p>
            <div style="display:flex; gap:12px; margin-top:18px;">
              <a class="btn btn-primary" href="products.html">立即选购</a>
              <a class="btn" href="story.html">了解品牌</a>
            </div>
          </div>
          <div class="hero-card reveal" style="transform:rotate(-3deg);">
            <div class="box-stack">
              <div class="choco-box choco-box--1">ORANGE</div>
              <div class="choco-box choco-box--2">HAPPI</div>
              <div class="choco-box choco-box--3">OAT MILK</div>
            </div>
          </div>
        </div>
      </section>

      <section class="section">
        <div class="container stickers reveal">
          <span class="sticker">I'M VEGAN</span>
          <span class="sticker">SOYA FREE</span>
          <span class="sticker">GLUTEN FREE</span>
          <span class="sticker">LOWER SUGAR</span>
        </div>
      </section>

      <section class="section">
        <div class="container reveal">
          <h2 class="section-title">人气口味</h2>
          <p class="section-sub">以卡片形式模拟包装视觉，支持响应式栅格。</p>
          <div class="grid grid-3">
            <article class="product-card reveal">
              <div class="product-art"></div>
              <div class="product-title">橙味 OAT MILK Chocolate</div>
              <div class="badge">Vegan</div>
              <div class="price">£2.99</div>
              <a class="btn btn-primary" href="products.html">查看</a>
            </article>
            <article class="product-card reveal">
              <div class="product-art" style="background:linear-gradient(135deg,#60a5fa,#2563eb);"></div>
              <div class="product-title">海盐可可</div>
              <div class="badge">Gluten Free</div>
              <div class="price">£2.99</div>
              <a class="btn btn-primary" href="products.html">查看</a>
            </article>
            <article class="product-card reveal">
              <div class="product-art" style="background:linear-gradient(135deg,#f97316,#ef4444);"></div>
              <div class="product-title">浓醇经典</div>
              <div class="badge">Lower Sugar</div>
              <div class="price">£3.49</div>
              <a class="btn btn-primary" href="products.html">查看</a>
            </article>
          </div>
        </div>
      </section>

      <section class="ooo-block">
        <div class="container reveal">
          <div class="ooo-text">We've got</div>
          <div class="ooo-row" style="margin:10px 0 20px;">
            <div class="ooo">O</div>
            <div class="ooo">O</div>
            <div class="ooo">O</div>
            <div class="ooo">O</div>
            <div class="ooo">O</div>
          </div>
          <div class="ooo-text">without the Moo</div>
        </div>
      </section>

      <section class="section" style="background: #ffefc3;">
        <div class="container grid grid-2 reveal">
          <div class="feature">
            <h3 class="section-title" style="font-size:36px;">We’re 100% Slave Free</h3>
            <p>取材于长图中部的价值宣言，这里仅进行纯视觉呈现。</p>
          </div>
          <div class="feature">
            <h3 class="section-title" style="font-size:36px;">47% Cacao · Soya Free</h3>
            <p>使用布局与色块模拟贴纸与手绘文字效果。</p>
          </div>
        </div>
      </section>

      <section class="section">
        <div class="container newsletter reveal">
          <h3 class="section-title" style="font-size:34px;">订阅我们的 Newsletter</h3>
          <p class="section-sub">输入邮箱即可“假装订阅”，目前不做交互逻辑。</p>
          <form action="#" onsubmit="return false;">
            <input type="email" placeholder="Your email" />
            <button class="btn btn-primary" type="submit">订阅</button>
          </form>
        </div>
      </section>
    </main>

    <footer>
      <div class="container" style="display:flex; justify-content:space-between; align-items:center; gap:16px;">
        <div>© 2025 Happi Demo</div>
        <div class="links">
          <a href="products.html">商店</a>
          <a href="story.html">关于我们</a>
          <a href="benefits.html">优势</a>
          <a href="subscribe.html">订阅</a>
        </div>
      </div>
    </footer>
    <script src="assets/js/app.js"></script>
  </body>
</html>


