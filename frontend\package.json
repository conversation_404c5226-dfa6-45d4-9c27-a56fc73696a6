{"name": "happi-chocolate-website", "version": "1.0.0", "description": "Happi chocolate brand demo website", "main": "frontend/index.html", "scripts": {"serve": "http-server ./frontend -p 8080 -c-1 --cors", "start": "npm run serve", "dev": "http-server ./frontend -p 8080 -c-1 --cors -o", "build": "echo This is a static site", "clean": "rm -rf node_modules package-lock.json", "test": "echo No tests specified"}, "keywords": ["chocolate", "website", "static", "frontend", "happi"], "author": "Happi Team", "license": "MIT", "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "."}}